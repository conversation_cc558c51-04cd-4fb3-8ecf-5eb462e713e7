<template>
	<view>
		
	</view>
</template>

<script>
import { getWorkOrderPageApi } from '../../../../../api/scm/mfg/workorder';

	export default {
		data() {
			return {
				workOrderList:[],
				requestParams:{
					pageSize:10,
					pageNo:1
				}
			}
		},
		methods: {
			async getWorkOrderList(){
				const response = await getWorkOrderPageApi(this.requestParams)
			}
		},
		mounted(){
			this.getWorkOrderList()
		}
	}
</script>

<style>

</style>
