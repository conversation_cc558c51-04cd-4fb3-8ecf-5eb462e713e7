<template>
	<view class="work-order-item">
		<view class="item-container" @click="handleItemClick">
			<!-- 头部信息：生产单号和状态 -->
			<view class="header-section">
				<view class="work-no-container">
					<text class="work-no">{{ item.workNo || '暂无单号' }}</text>
					<view class="copy-btn" @click.stop="copyWorkNo" v-if="item.workNo">
						<uni-icons type="copy" size="14" color="#666"></uni-icons>
					</view>
				</view>
				<view class="status-badges">
					<view class="status-tag" :class="'status-' + item.status">
						{{ getStatusText(item.status) }}
					</view>
					<view class="approve-tag" :class="'approve-' + item.approveStatus" v-if="item.approveStatus !== undefined">
						{{ getApproveStatusText(item.approveStatus) }}
					</view>
				</view>
			</view>

			<!-- 产品信息 -->
			<view class="product-section">
				<view class="product-info">
					<text class="product-name">{{ item.productName || '未知产品' }}</text>
					<text class="product-code" v-if="item.productCode">{{ item.productCode }}</text>
				</view>
				<view class="product-spec" v-if="item.spec">
					<text class="spec-text">{{ item.spec }}</text>
				</view>
			</view>

			<!-- 数量和时间信息 -->
			<view class="details-section">
				<view class="detail-row">
					<view class="detail-item">
						<text class="detail-label">订单数量</text>
						<text class="detail-value">{{ formatQuantity(item.orderQuantity) }} {{ getUnitText(item.orderUnit) }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">计划数量</text>
						<text class="detail-value">{{ formatQuantity(item.scheduleQuantity) }} {{ getUnitText(item.orderUnit) }}</text>
					</view>
				</view>

				<view class="detail-row" v-if="item.scheduleStartTime || item.scheduleEndTime">
					<view class="detail-item">
						<text class="detail-label">开始时间</text>
						<text class="detail-value">{{ formatDateTime(item.scheduleStartTime) }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">结束时间</text>
						<text class="detail-value">{{ formatDateTime(item.scheduleEndTime) }}</text>
					</view>
				</view>
			</view>

			<!-- 进度条 -->
			<view class="progress-section" v-if="item.progress !== undefined && item.progress !== null">
				<view class="progress-header">
					<text class="progress-label">完成进度</text>
					<text class="progress-text">{{ formatProgress(item.progress) }}%</text>
				</view>
				<view class="progress-bar">
					<view class="progress-fill" :style="{ width: formatProgress(item.progress) + '%', backgroundColor: getProgressColor(item.progress) }"></view>
				</view>
			</view>

			<!-- 其他状态信息 -->
			<view class="status-section" v-if="hasStatusInfo">
				<view class="status-row">
					<view class="status-item" v-if="item.pickingStatus !== undefined">
						<text class="status-label">领料</text>
						<view class="status-badge" :class="'picking-' + item.pickingStatus">
							{{ getTaskStatusText(item.pickingStatus) }}
						</view>
					</view>
					<view class="status-item" v-if="item.reportStatus !== undefined">
						<text class="status-label">报工</text>
						<view class="status-badge" :class="'report-' + item.reportStatus">
							{{ getTaskStatusText(item.reportStatus) }}
						</view>
					</view>
					<view class="status-item" v-if="item.qualityStatus !== undefined">
						<text class="status-label">质检</text>
						<view class="status-badge" :class="'quality-' + item.qualityStatus">
							{{ getTaskStatusText(item.qualityStatus) }}
						</view>
					</view>
					<view class="status-item" v-if="item.inStockStatus !== undefined">
						<text class="status-label">入库</text>
						<view class="status-badge" :class="'stock-' + item.inStockStatus">
							{{ getTaskStatusText(item.inStockStatus) }}
						</view>
					</view>
				</view>
			</view>

			<!-- 底部信息 -->
			<view class="footer-section" v-if="item.customerName || item.orderNo || item.createTime">
				<view class="footer-row" v-if="item.customerName">
					<text class="footer-label">客户：</text>
					<text class="footer-value">{{ item.customerName }}</text>
				</view>
				<view class="footer-row" v-if="item.orderNo">
					<text class="footer-label">来源单号：</text>
					<text class="footer-value">{{ item.orderNo }}</text>
				</view>
				<view class="footer-row" v-if="item.createTime">
					<text class="footer-label">创建时间：</text>
					<text class="footer-value">{{ formatDateTime(item.createTime) }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'WorkOrderListItem',
	props: {
		item: {
			type: Object,
			required: true,
			default: () => ({})
		}
	},
	data() {
		return {
			// 状态映射
			statusMap: {
				0: '待投料',
				1: '投料中',
				2: '生产中',
				3: '待质检',
				4: '已完成',
				5: '已取消'
			},
			approveStatusMap: {
				0: '待提交',
				1: '审核中',
				2: '已拒绝',
				3: '已通过'
			},
			taskStatusMap: {
				0: '未开始',
				1: '进行中',
				2: '已完成',
				3: '已取消'
			}
		}
	},
	computed: {
		// 是否有状态信息需要显示
		hasStatusInfo() {
			return this.item.pickingStatus !== undefined ||
				   this.item.reportStatus !== undefined ||
				   this.item.qualityStatus !== undefined ||
				   this.item.inStockStatus !== undefined;
		}
	},
	methods: {
		// 处理列表项点击
		handleItemClick() {
			this.$emit('click', this.item);
		},

		// 复制生产单号
		copyWorkNo() {
			if (this.item.workNo) {
				// #ifdef H5
				if (navigator.clipboard) {
					navigator.clipboard.writeText(this.item.workNo).then(() => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					});
				} else {
					// 降级方案
					const textArea = document.createElement('textarea');
					textArea.value = this.item.workNo;
					document.body.appendChild(textArea);
					textArea.select();
					document.execCommand('copy');
					document.body.removeChild(textArea);
					uni.showToast({
						title: '复制成功',
						icon: 'success'
					});
				}
				// #endif

				// #ifdef MP-WEIXIN
				uni.setClipboardData({
					data: this.item.workNo,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					}
				});
				// #endif

				// #ifdef APP-PLUS
				uni.setClipboardData({
					data: this.item.workNo,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					}
				});
				// #endif
			}
		},

		// 获取状态文本
		getStatusText(status) {
			return this.statusMap[status] || '未知状态';
		},

		// 获取审批状态文本
		getApproveStatusText(status) {
			return this.approveStatusMap[status] || '未知';
		},

		// 获取任务状态文本
		getTaskStatusText(status) {
			return this.taskStatusMap[status] || '未知';
		},

		// 格式化数量
		formatQuantity(value) {
			if (value === null || value === undefined || value === '') return '0';
			if (isNaN(value)) return '0';
			return Number(value).toLocaleString();
		},

		// 获取单位文本
		getUnitText(unit) {
			// 这里可以根据实际的单位映射来处理
			// 暂时直接返回，后续可以接入单位字典
			return unit || '';
		},

		// 格式化日期时间
		formatDateTime(timestamp) {
			if (!timestamp) return '-';
			const date = new Date(timestamp);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		},

		// 格式化进度
		formatProgress(progress) {
			if (progress === null || progress === undefined) return 0;
			const num = parseFloat(progress);
			return isNaN(num) ? 0 : Math.min(100, Math.max(0, num));
		},

		// 获取进度条颜色
		getProgressColor(progress) {
			const num = this.formatProgress(progress);
			if (num >= 100) return '#67c23a'; // 绿色 - 已完成
			if (num >= 80) return '#409eff';  // 蓝色 - 接近完成
			if (num >= 50) return '#e6a23c';  // 橙色 - 进行中
			if (num > 0) return '#f56c6c';    // 红色 - 刚开始
			return '#dcdfe6';                 // 灰色 - 未开始
		}
	}
}
</script>

<style lang="scss" scoped>
.work-order-item {
	margin-bottom: 24rpx;
	background-color: white;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
	border: 1px solid #f0f0f0;
	overflow: hidden;
	transition: box-shadow 0.2s, transform 0.2s;

	&:active {
		transform: translateY(1rpx);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
	}
}

.item-container {
	padding: 28rpx;
}

/* 头部区域 */
.header-section {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 24rpx;
}

.work-no-container {
	display: flex;
	align-items: center;
	flex: 1;

	.work-no {
		font-size: 32rpx;
		font-weight: 600;
		color: #2c3e50;
		margin-right: 16rpx;
	}

	.copy-btn {
		padding: 8rpx;
		border-radius: 8rpx;
		background-color: #f8f9fa;
		transition: background-color 0.2s;

		&:active {
			background-color: #e9ecef;
		}
	}
}

.status-badges {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 8rpx;
}

.status-tag, .approve-tag {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
	color: white;
	text-align: center;
	min-width: 120rpx;
}

/* 工作订单状态样式 */
.status-0 { background-color: #909399; } /* 待投料 */
.status-1 { background-color: #e6a23c; } /* 投料中 */
.status-2 { background-color: #409eff; } /* 生产中 */
.status-3 { background-color: #f56c6c; } /* 待质检 */
.status-4 { background-color: #67c23a; } /* 已完成 */
.status-5 { background-color: #c0c4cc; } /* 已取消 */

/* 审批状态样式 */
.approve-0 { background-color: #909399; } /* 待提交 */
.approve-1 { background-color: #e6a23c; } /* 审核中 */
.approve-2 { background-color: #f56c6c; } /* 已拒绝 */
.approve-3 { background-color: #67c23a; } /* 已通过 */

/* 产品信息区域 */
.product-section {
	margin-bottom: 24rpx;
}

.product-info {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;

	.product-name {
		font-size: 30rpx;
		font-weight: 600;
		color: #2c3e50;
		margin-right: 16rpx;
		flex: 1;
	}

	.product-code {
		font-size: 24rpx;
		color: #6c757d;
		background-color: #f8f9fa;
		padding: 6rpx 12rpx;
		border-radius: 8rpx;
	}
}

.product-spec {
	.spec-text {
		font-size: 26rpx;
		color: #6c757d;
	}
}

/* 详情区域 */
.details-section {
	margin-bottom: 24rpx;
}

.detail-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.detail-item {
	flex: 1;

	.detail-label {
		font-size: 24rpx;
		color: #6c757d;
		display: block;
		margin-bottom: 6rpx;
	}

	.detail-value {
		font-size: 26rpx;
		color: #2c3e50;
		font-weight: 500;
	}
}

/* 进度区域 */
.progress-section {
	margin-bottom: 24rpx;
}

.progress-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;

	.progress-label {
		font-size: 26rpx;
		color: #6c757d;
	}

	.progress-text {
		font-size: 26rpx;
		color: #2c3e50;
		font-weight: 600;
	}
}

.progress-bar {
	height: 12rpx;
	background-color: #e9ecef;
	border-radius: 6rpx;
	overflow: hidden;

	.progress-fill {
		height: 100%;
		border-radius: 6rpx;
		transition: width 0.3s ease;
	}
}

/* 状态区域 */
.status-section {
	margin-bottom: 24rpx;
}

.status-row {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.status-item {
	display: flex;
	align-items: center;
	gap: 8rpx;

	.status-label {
		font-size: 24rpx;
		color: #6c757d;
	}

	.status-badge {
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		font-size: 22rpx;
		font-weight: 500;
		color: white;
	}
}

/* 任务状态样式 */
.picking-0, .report-0, .quality-0, .stock-0 { background-color: #909399; } /* 未开始 */
.picking-1, .report-1, .quality-1, .stock-1 { background-color: #e6a23c; } /* 进行中 */
.picking-2, .report-2, .quality-2, .stock-2 { background-color: #67c23a; } /* 已完成 */
.picking-3, .report-3, .quality-3, .stock-3 { background-color: #c0c4cc; } /* 已取消 */

/* 底部区域 */
.footer-section {
	border-top: 1px solid #f0f0f0;
	padding-top: 20rpx;
}

.footer-row {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;

	&:last-child {
		margin-bottom: 0;
	}

	.footer-label {
		font-size: 24rpx;
		color: #6c757d;
		min-width: 140rpx;
	}

	.footer-value {
		font-size: 24rpx;
		color: #2c3e50;
		flex: 1;
		word-break: break-all;
	}
}
</style>